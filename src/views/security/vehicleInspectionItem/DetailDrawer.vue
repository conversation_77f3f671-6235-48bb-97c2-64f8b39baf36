<template>
  <BasicDrawer
    v-bind="$attrs"
    title="检修项详情-发动机日常检查"
    :width="920"
    :footer="false"
    @register="registerInnerDrawer"
  >
    <div v-loading="loading" class="detail-container">
      <Row :gutter="16" class="mb-4">
        <!-- 左侧：基本信息 -->
        <Col :span="12">
          <div class="info-section">
            <div class="section-title app-marker"> 基本信息 </div>
            <div class="info-grid !h-72 !overflow-auto">
              <div class="info-item">
                <span class="label">检修项编号</span>
                <span class="value">{{ detailData?.itemNum || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">检修项名称</span>
                <span class="value">{{ detailData?.itemName || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">适用车型</span>
                <span class="value">{{ detailData?.vehicleType || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">部件类型</span>
                <span class="value">{{ detailData?.componentType || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">检修频率</span>
                <span class="value">每日</span>
              </div>
              <div class="info-item">
                <span class="label">检修点数</span>
                <span class="value">{{ detailData?.pointCount || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="label">重点检修点数</span>
                <span class="value">{{ keyPointCount }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态</span>
                <span class="value">
                  <Tag color="green">{{ getStatusText(detailData?.status) }}</Tag>
                </span>
              </div>
              <div class="info-item">
                <span class="label">创建人</span>
                <span class="value">{{ detailData?.createUser || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间</span>
                <span class="value">{{ detailData?.createTime || '-' }}</span>
              </div>
            </div>
          </div>
        </Col>

        <Col :span="12">
          <!-- 检修标准流程 -->
          <div class="info-section">
            <div class="section-title app-marker"> 检修标准流程 </div>
            <div class="process-list !h-72 !overflow-auto">
              <div v-for="(step, index) in processSteps" :key="index" class="process-item">
                <!-- <span class="step-number">{{ index + 1 }}.</span> -->
                <span class="step-content">{{ step }}</span>
              </div>
            </div>
          </div>
        </Col>
      </Row>
      <Row :gutter="16">
        <Col :span="12">
          <div class="info-section">
            <div class="section-title app-marker"> 示意图 </div>
            <div class="image-container flex items-center justify-center bg-gray-200 p-px">
              <Image
                v-if="detailData?.itemImg"
                :src="detailData.itemImg"
                alt="检修项示意图"
                class="!w-full !min-h-[160px] object-contain"
              />
              <div v-else class="no-image !h-[160px]">
                <Icon icon="ant-design:picture-outlined" class="text-gray-400" :size="48" />
                <span class="text-gray-500 mt-1">暂无示意图</span>
              </div>
            </div>
          </div>
        </Col>

        <!-- 右侧：检修分布图表 -->
        <Col :span="12">
          <div class="info-section">
            <div class="section-title app-marker"> 检修点分布 </div>
            <div class="chart-container flex h-[160px]">
              <div ref="chartRef" class="chart flex-none w-1/2 !h-full"></div>
              <div class="chart-legend flex-1 w-0">
                <div class="legend-item">
                  <div class="legend-color" style="background-color: #ff4d4f"></div>
                  <span>重点检修点</span>
                  <span class="legend-value">{{ keyPointCount }}</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color" style="background-color: #7b96b9"></div>
                  <span>非重点检修点</span>
                  <span class="legend-value">{{ normalPointCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </Col>
      </Row>

      <!-- 下半部分：检修点列表 -->
      <div class="table-section mt-4">
        <!-- <div class="section-title mb-4">
          <Icon icon="ant-design:table-outlined" class="mr-2" />
          检修点列表
        </div> -->
        <CheckpointTable
          :inspection-item-id="currentInspectionItemId"
          @points-change="handlePointsChange"
          ref="checkpointTableRef"
        />
      </div>
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick } from 'vue';
  import { Row, Col, Tag, Image } from 'ant-design-vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { inspectionItemDetail } from '@/api/security/vehicleInspectionItem';
  import CheckpointTable from './CheckpointModal/CheckpointTable.vue';

  defineOptions({ name: 'InspectionItemDetailDrawer' });

  const loading = ref(false);
  const detailData = ref<any>(null);
  const pointsData = ref<any[]>([]);
  const chartRef = ref<HTMLDivElement>() as any;
  const currentInspectionItemId = ref<string | number>('');
  const checkpointTableRef = ref<any>(null);

  // 计算属性
  const keyPointCount = computed(() => {
    return pointsData.value.filter((item: any) => item.isKeyPoint === 1).length;
  });

  const normalPointCount = computed(() => {
    return pointsData.value.filter((item: any) => item.isKeyPoint === 0).length;
  });

  const processSteps = computed(() => {
    if (!detailData.value?.standardProcess) return [];
    // 假设标准流程是用换行符分隔的步骤
    return detailData.value.standardProcess.split('\n').filter((step: string) => step.trim());
  });

  // 状态文本映射
  const getStatusText = (status: string) => {
    const statusMap = {
      '1': '启用',
      '0': '停用',
      active: '启用',
      inactive: '停用',
    };
    return statusMap[status] || '未知';
  };

  // ECharts 图表
  const { setOptions: setChartOptions } = useECharts(chartRef);

  // 更新图表
  const updateChart = () => {
    if (!chartRef.value) return;

    const option: any = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      series: [
        {
          name: '检修点分布',
          type: 'pie',
          radius: ['50%', '80%'],
          center: ['50%', '50%'],
          data: [
            {
              value: keyPointCount.value,
              name: '重点检修点',
              itemStyle: { color: '#ff4d4f' },
            },
            {
              value: normalPointCount.value,
              name: '非重点检修点',
              itemStyle: { color: '#7B96B9' },
            },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
        },
      ],
    };

    setChartOptions(option);
  };

  // 处理检修点数据变化
  const handlePointsChange = (points: any[]) => {
    pointsData.value = points;
    // 更新图表
    nextTick(() => {
      updateChart();
    });
  };

  // Drawer 注册
  const [registerInnerDrawer] = useDrawerInner(async (inspectionItemId: string | number) => {
    if (!inspectionItemId) return;

    loading.value = true;

    try {
      // 获取检修项详情
      const detailResponse = await inspectionItemDetail(inspectionItemId);
      detailData.value = detailResponse;
      currentInspectionItemId.value = detailResponse.itemNum;

      // CheckpointTable 组件会自动加载检修点数据
      // 等待一下让 CheckpointTable 组件加载完成
      nextTick(() => {
        if (checkpointTableRef.value) {
          // 如果需要，可以手动触发检修点数据加载
          // checkpointTableRef.value.reload();
        }
      });
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  });
</script>

<style scoped lang="less">
  .detail-container {
    .info-section {
      padding: 20px;
      border-radius: 8px;
      background: rgba(var(--color-primary-500), 0.04);

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 16px;

        .info-item {
          display: flex;
          align-items: center;

          .label {
            width: 120px;
            color: #666;
            font-size: 14px;
          }

          .value {
            flex: 1;
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }

      .process-list {
        .process-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 12px;

          .step-number {
            flex-shrink: 0;
            width: 24px;
            color: #1890ff;
            font-weight: 600;
          }

          .step-content {
            flex: 1;
            color: #333;
            line-height: 1.5;
          }
        }
      }

      .image-container {
        .no-image {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }

      .chart-container {
        .chart {
          margin-bottom: 16px;
        }

        .chart-legend {
          display: flex;
          flex-direction: column;
          justify-content: center;
          gap: 24px;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;

            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 50%;
            }

            .legend-value {
              color: #333;
              font-weight: 600;
            }
          }
        }
      }
    }

    .table-section mt-4 {
      border-radius: 8px;
      background: rgba(var(--color-primary-500), 0.04);

      .section-title {
        display: flex;
        align-items: center;
        color: #1890ff;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
</style>
