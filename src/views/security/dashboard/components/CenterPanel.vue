<template>
  <div class="center-panel">
    <!-- 中国3D地图 -->
    <div class="map-container">
      <div class="map-header">
        <h3 class="map-title">全国分布概览</h3>
      </div>
      <div ref="mapChartRef" class="map-chart"></div>
    </div>

    <!-- 关键指标概览 -->
    <div class="metrics-container">
      <div class="metrics-grid">
        <div 
          v-for="(metric, index) in metricsData" 
          :key="index"
          class="metric-card"
          :style="{ background: metric.background }"
        >
          <div class="metric-content">
            <div class="metric-header">
              <span class="metric-title">{{ metric.title }}</span>
              <Icon :icon="metric.icon" :size="24" class="metric-icon" />
            </div>
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-change" :style="{ color: metric.changeColor }">
              <Icon :icon="metric.changeIcon" :size="14" />
              <span>{{ metric.change }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { Icon } from '@/components/Icon';
  import { useECharts } from '@/hooks/web/useECharts';

  const mapChartRef = ref();
  const { setOptions: setMapOptions } = useECharts(mapChartRef);

  // 关键指标数据
  const metricsData = ref([
    {
      title: '今日活跃次数',
      value: '12',
      change: '+8%',
      changeColor: '#E67E22',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #E74C3C 0%, #C0392B 100%)',
      icon: 'ant-design:fire-outlined'
    },
    {
      title: '今日作业任务',
      value: '47',
      change: '+8%',
      changeColor: '#3498DB',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #26B99A 0%, #1ABC9C 100%)',
      icon: 'ant-design:tool-outlined'
    },
    {
      title: 'AR眼镜使用次数',
      value: '32',
      change: '+5%',
      changeColor: '#5DADE2',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #3498DB 0%, #2980B9 100%)',
      icon: 'ant-design:eye-outlined'
    },
    {
      title: '工程车辆检修',
      value: '8',
      change: '30%',
      changeColor: '#95A5A6',
      changeIcon: 'ant-design:percentage-outlined',
      background: 'linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%)',
      icon: 'ant-design:car-outlined'
    }
  ]);

  // 地图数据
  const mapData = [
    { name: '成都', value: [104.066, 30.572, 85], itemStyle: { color: '#26B99A' } },
    { name: '石家庄', value: [114.514, 38.042, 72], itemStyle: { color: '#3498DB' } }
  ];

  // 初始化地图
  function initMap() {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' },
        formatter: function(params) {
          if (params.seriesType === 'scatter') {
            return `${params.name}<br/>活跃度: ${params.value[2]}`;
          }
          return params.name;
        }
      },
      geo: {
        map: 'china',
        roam: false,
        zoom: 1.2,
        center: [107, 36],
        itemStyle: {
          areaColor: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(46, 134, 193, 0.8)' },
              { offset: 1, color: 'rgba(11, 36, 59, 0.9)' }
            ]
          },
          borderColor: 'rgba(255, 255, 255, 0.3)',
          borderWidth: 1
        },
        emphasis: {
          itemStyle: {
            areaColor: 'rgba(46, 134, 193, 1)',
            borderColor: 'rgba(255, 255, 255, 0.8)',
            borderWidth: 2
          }
        }
      },
      series: [
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          data: mapData,
          symbolSize: function(val) {
            return Math.max(val[2] / 4, 15);
          },
          symbol: 'circle',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(255, 255, 255, 0.5)'
          },
          emphasis: {
            scale: 1.5,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(255, 255, 255, 0.8)'
            }
          }
        },
        {
          type: 'effectScatter',
          coordinateSystem: 'geo',
          data: mapData,
          symbolSize: function(val) {
            return Math.max(val[2] / 6, 10);
          },
          showEffectOn: 'render',
          rippleEffect: {
            brushType: 'stroke',
            scale: 2.5,
            period: 4
          },
          itemStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
            shadowBlur: 10,
            shadowColor: 'rgba(255, 255, 255, 0.5)'
          }
        }
      ]
    };
    setMapOptions(option);
  }

  onMounted(() => {
    // 需要先加载中国地图数据
    import('echarts/map/js/china.js').then(() => {
      setTimeout(() => {
        initMap();
      }, 100);
    });
  });
</script>

<style scoped>
  .center-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .map-container {
    flex: 1;
    background: linear-gradient(135deg, rgba(11, 36, 59, 0.9) 0%, rgba(46, 134, 193, 0.6) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
  }

  .map-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .map-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    text-align: center;
  }

  .map-chart {
    height: calc(100% - 80px);
    min-height: 300px;
  }

  .metrics-container {
    height: 140px;
    flex-shrink: 0;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    height: 100%;
  }

  .metric-card {
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }

  .metric-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .metric-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
  }

  .metric-title {
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.2;
  }

  .metric-icon {
    color: rgba(255, 255, 255, 0.8);
  }

  .metric-value {
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin: 8px 0;
  }

  .metric-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 12px;
    }

    .metrics-container {
      height: 180px;
    }

    .metric-content {
      padding: 15px;
    }

    .metric-value {
      font-size: 28px;
    }
  }

  @media (max-width: 1200px) {
    .map-chart {
      min-height: 250px;
    }

    .metrics-grid {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: 1fr;
    }

    .metrics-container {
      height: 120px;
    }

    .metric-content {
      padding: 12px;
    }

    .metric-title {
      font-size: 12px;
    }

    .metric-value {
      font-size: 24px;
    }
  }

  @media (max-width: 768px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
    }

    .metrics-container {
      height: 160px;
    }
  }
</style>
