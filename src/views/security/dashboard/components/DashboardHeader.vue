<template>
  <div class="dashboard-header">
    <!-- 左侧标题 -->
    <div class="header-left">
      <h1 class="header-title">
        <span class="title-zh">数据概览</span>
        <span class="title-en">Data Overview</span>
      </h1>
    </div>

    <!-- 右侧功能区 -->
    <div class="header-right">
      <!-- 当前时间 -->
      <div class="current-time">
        <span class="time-text">{{ currentTime }}</span>
      </div>

      <!-- 全屏按钮 -->
      <div class="fullscreen-btn" @click="handleFullscreen">
        <Icon :icon="isFullscreen ? 'ant-design:fullscreen-exit-outlined' : 'ant-design:fullscreen-outlined'" :size="24" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { Icon } from '@/components/Icon';

  const emit = defineEmits(['toggle-fullscreen']);

  const currentTime = ref('');
  const isFullscreen = ref(false);
  let timeInterval = null;

  // 更新时间
  function updateTime() {
    const now = new Date();
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[now.getDay()];
    const time = now.toLocaleTimeString('zh-CN', { hour12: false });
    const date = now.toLocaleDateString('zh-CN').replace(/\//g, '/');
    
    currentTime.value = `${weekday} ${time} ${date}`;
  }

  // 处理全屏切换
  function handleFullscreen() {
    isFullscreen.value = !isFullscreen.value;
    emit('toggle-fullscreen');
  }

  // 监听全屏状态变化
  function handleFullscreenChange() {
    isFullscreen.value = !!document.fullscreenElement;
  }

  onMounted(() => {
    updateTime();
    timeInterval = setInterval(updateTime, 1000);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
  });
</script>

<style scoped>
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    padding: 0 40px;
    background: linear-gradient(135deg, rgba(11, 36, 59, 0.9) 0%, rgba(46, 134, 193, 0.8) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .header-title {
    margin: 0;
    display: flex;
    flex-direction: column;
    line-height: 1.2;
  }

  .title-zh {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 2px;
  }

  .title-en {
    font-size: 14px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 2px;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 30px;
  }

  .current-time {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .time-text {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    font-family: 'Consolas', 'Monaco', monospace;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .fullscreen-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    color: #ffffff;
  }

  .fullscreen-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .fullscreen-btn:active {
    transform: translateY(0);
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .dashboard-header {
      padding: 0 20px;
      height: 70px;
    }

    .title-zh {
      font-size: 24px;
    }

    .title-en {
      font-size: 12px;
    }

    .time-text {
      font-size: 14px;
    }

    .header-right {
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    .dashboard-header {
      padding: 0 15px;
      height: 60px;
    }

    .title-zh {
      font-size: 20px;
    }

    .title-en {
      display: none;
    }

    .current-time {
      padding: 8px 12px;
    }

    .time-text {
      font-size: 12px;
    }

    .fullscreen-btn {
      width: 40px;
      height: 40px;
    }
  }
</style>
