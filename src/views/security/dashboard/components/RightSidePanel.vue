<template>
  <div class="right-side-panel">
    <!-- 模型总调用趋势 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">模型总调用趋势</h3>
        <div class="card-controls">
          <Select v-model:value="modelCallPeriod" style="width: 120px" size="small">
            <SelectOption value="1">近1个月</SelectOption>
            <SelectOption value="3">近3个月</SelectOption>
            <SelectOption value="6">近6个月</SelectOption>
          </Select>
        </div>
      </div>
      
      <div class="card-content">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #16A085"></span>
            <span class="legend-text">模型总调用次数</span>
          </div>
        </div>
        
        <div ref="modelCallChartRef" class="chart-container"></div>
      </div>
    </div>

    <!-- 作业管控情况统计 -->
    <div class="panel-card">
      <div class="card-header">
        <h3 class="card-title">作业管控情况统计</h3>
        <div class="card-controls">
          <Select v-model:value="workControlPeriod" style="width: 120px" size="small">
            <SelectOption value="1">近1个月</SelectOption>
            <SelectOption value="3">近3个月</SelectOption>
            <SelectOption value="6">近6个月</SelectOption>
          </Select>
          <Button type="primary" size="small" @click="exportWorkControlData">
            <Icon icon="ant-design:export-outlined" />
            导出
          </Button>
        </div>
      </div>
      
      <div class="card-content">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #1F4E79"></span>
            <span class="legend-text">检修次数</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot" style="background-color: #E67E22"></span>
            <span class="legend-text">异常数量</span>
          </div>
        </div>
        
        <div ref="workControlChartRef" class="chart-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { Select, SelectOption, Button, message } from 'ant-design-vue';
  import { Icon } from '@/components/Icon';
  import { useECharts } from '@/hooks/web/useECharts';

  const modelCallPeriod = ref('1');
  const workControlPeriod = ref('1');
  
  const modelCallChartRef = ref();
  const workControlChartRef = ref();

  const { setOptions: setModelCallOptions } = useECharts(modelCallChartRef);
  const { setOptions: setWorkControlOptions } = useECharts(workControlChartRef);

  // Mock数据
  const modelCallData = {
    '1': {
      dates: ['10-01', '10-05', '10-10', '10-15', '10-20', '10-24'],
      calls: [1200, 1350, 1180, 1420, 1380, 1500]
    },
    '3': {
      dates: ['8月', '9月', '10月'],
      calls: [35000, 38000, 42000]
    },
    '6': {
      dates: ['5月', '6月', '7月', '8月', '9月', '10月'],
      calls: [28000, 32000, 35000, 35000, 38000, 42000]
    }
  };

  const workControlData = {
    '1': {
      weeks: ['第一周', '第二周', '第三周', '第四周'],
      inspectionCount: [42, 48, 35, 45],
      exceptionCount: [7, 11, 5, 9]
    },
    '3': {
      weeks: ['第一月', '第二月', '第三月'],
      inspectionCount: [165, 180, 170],
      exceptionCount: [28, 35, 30]
    },
    '6': {
      weeks: ['第一季度', '第二季度'],
      inspectionCount: [515, 550],
      exceptionCount: [88, 95]
    }
  };

  // 初始化模型调用趋势图表
  function initModelCallChart() {
    const data = modelCallData[modelCallPeriod.value];
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.dates,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' }
      },
      yAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
        splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
      },
      series: [
        {
          name: '模型总调用次数',
          type: 'line',
          data: data.calls,
          smooth: true,
          lineStyle: { 
            color: '#16A085', 
            width: 3,
            shadowColor: 'rgba(22, 160, 133, 0.5)',
            shadowBlur: 10
          },
          itemStyle: { color: '#16A085' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(22, 160, 133, 0.6)' },
                { offset: 1, color: 'rgba(22, 160, 133, 0.1)' }
              ]
            }
          },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(22, 160, 133, 0.8)'
            }
          }
        }
      ]
    };
    setModelCallOptions(option);
  }

  // 初始化作业管控情况图表
  function initWorkControlChart() {
    const data = workControlData[workControlPeriod.value];
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.weeks,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.8)' }
      },
      yAxis: [
        {
          type: 'value',
          name: '检修次数',
          position: 'left',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
        },
        {
          type: 'value',
          name: '异常数量',
          position: 'right',
          axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
          axisLabel: { color: 'rgba(255, 255, 255, 0.8)' }
        }
      ],
      series: [
        {
          name: '检修次数',
          type: 'line',
          yAxisIndex: 0,
          data: data.inspectionCount,
          lineStyle: { 
            color: '#1F4E79', 
            width: 3,
            shadowColor: 'rgba(31, 78, 121, 0.5)',
            shadowBlur: 10
          },
          itemStyle: { color: '#1F4E79' },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(31, 78, 121, 0.8)'
            }
          }
        },
        {
          name: '异常数量',
          type: 'line',
          yAxisIndex: 1,
          data: data.exceptionCount,
          lineStyle: { 
            color: '#E67E22', 
            width: 3,
            shadowColor: 'rgba(230, 126, 34, 0.5)',
            shadowBlur: 10
          },
          itemStyle: { color: '#E67E22' },
          symbol: 'circle',
          symbolSize: 8,
          emphasis: {
            scale: 1.2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(230, 126, 34, 0.8)'
            }
          }
        }
      ]
    };
    setWorkControlOptions(option);
  }

  // 导出数据
  function exportWorkControlData() {
    message.success('作业管控数据导出成功');
  }

  // 监听时间段变化
  watch(modelCallPeriod, initModelCallChart);
  watch(workControlPeriod, initWorkControlChart);

  onMounted(() => {
    setTimeout(() => {
      initModelCallChart();
      initWorkControlChart();
    }, 100);
  });
</script>

<style scoped>
  .right-side-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .panel-card {
    flex: 1;
    background: linear-gradient(135deg, rgba(11, 36, 59, 0.9) 0%, rgba(46, 134, 193, 0.6) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .card-controls {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .card-content {
    padding: 20px;
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
  }

  .chart-legend {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .legend-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
  }

  .chart-container {
    flex: 1;
    min-height: 200px;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .card-header {
      padding: 15px;
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
    }

    .card-controls {
      align-self: flex-end;
    }

    .card-title {
      font-size: 16px;
    }

    .chart-legend {
      gap: 15px;
    }
  }
</style>
