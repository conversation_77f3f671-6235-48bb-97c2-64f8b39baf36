import { DescItem } from '@/components/Description';
import { Tag } from 'ant-design-vue';
// import { dateUtil } from '@/utils/dateUtil';

export const descSchema: DescItem[] = [
  {
    label: '设备编号',
    field: 'deviceNo',
  },
  {
    label: '使用人员工号',
    field: 'userId',
  },
  {
    label: '使用人员名称',
    field: 'userName',
  },
  // {
  //   label: '站点名称',
  //   field: 'stationName',
  // },
  {
    label: '登录时间',
    field: 'loginTime',
    render(value) {
      if (!value) return '未设置';
      return (
        <div class="flex gap-2">
          {value}
          {/* <Tag bordered={false} color="green">
            开始使用
          </Tag> */}
        </div>
      );
    },
  },
  {
    label: '退出时间',
    field: 'logoutTime',
    render(value) {
      if (!value) return '未设置';
      return (
        <div class="flex gap-2">
          {value}
          {/* <Tag bordered={false} color="red">
            结束使用
          </Tag> */}
        </div>
      );
    },
  },
  {
    label: '心跳时间',
    field: 'lastActiveTime',
    render(value) {
      if (!value) return '未设置';
      return (
        <div class="flex gap-2">
          {value}
          {/* <Tag bordered={false} color="red">
            结束使用
          </Tag> */}
        </div>
      );
    },
  },
  {
    label: '使用时长',
    field: 'totalDurationStr',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
];
