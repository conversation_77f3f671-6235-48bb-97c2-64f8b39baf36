<template>
  <div class="task-stats-overview bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div
        v-for="(item, index) of statsModel"
        :key="index"
        class="stat-card bg-[rgba(0,52,119,0.04)] rounded-lg p-4 flex items-center space-x-2 cursor-pointer"
      >
        <img :src="item.icon" alt="" class="object-contain size-12" />

        <div class="flex-1 w-0">
          <span class="text-base text-gray-900">{{ item.label }}</span>
        </div>

        <div class="flex-none text-3xl font-bold text-primary-500">
          {{ item.value || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue';
  import { glassUsageLogCount } from '@/api/security/glassUsageLog';
  import stats1 from '@/assets/images/vehicleInspectionTask/stats-1.png?url';
  import stats2 from '@/assets/images/vehicleInspectionTask/stats-2.png?url';
  import stats3 from '@/assets/images/vehicleInspectionTask/stats-3.png?url';
  // import stats4 from '@/assets/images/vehicleInspectionTask/stats-4.png?url';

  defineOptions({ name: 'GlassesStatsOverview' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const statsData = ref<{
    totalUesd: number;
    totalDevices: number;
    totalDuration: number;
    totalDurationStr: string;
  }>({
    totalUesd: 0,
    totalDevices: 0,
    totalDuration: 0,
    totalDurationStr: '0分钟',
  });

  const statsModel = computed(() => {
    return [
      {
        label: '总使用次数',
        value: statsData.value.totalUesd,
        icon: stats1,
      },
      {
        label: '总设备数',
        value: statsData.value.totalDevices,
        icon: stats2,
      },
      {
        label: '总使用时长',
        value: statsData.value.totalDurationStr,
        icon: stats3,
      },
    ];
  });

  // 获取统计数据
  async function fetchStatsData(params?: any) {
    try {
      const response = await glassUsageLogCount(params);
      statsData.value = response || {
        totalUesd: 0,
        totalDevices: 0,
        totalDuration: 0,
        totalDurationStr: '0分钟',
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      statsData.value = {
        totalUesd: 0,
        totalDevices: 0,
        totalDuration: 0,
        totalDurationStr: '0分钟',
      };
    }
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    (newParams) => {
      fetchStatsData(newParams);
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchStatsData(props.searchParams);
  });

  // 暴露刷新方法供父组件调用
  defineExpose({
    refresh: (params?: any) => fetchStatsData(params),
  });
</script>

<style scoped>
  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
</style>
