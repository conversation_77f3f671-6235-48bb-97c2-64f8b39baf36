<template>
  <BasicModal
    v-bind="$attrs"
    title="AR眼镜使用记录详情"
    @register="registerInnerModal"
    :canFullscreen="false"
    :footer="null"
    width="800px"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 6 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './glassUsageLog.info';
  import { Skeleton } from 'ant-design-vue';
  import { glassUsageLogInfo } from '@/api/security/glassUsageLog';

  defineOptions({ name: 'GlassUsageLogInfoModal' });

  const showSkeleton = ref<boolean>(true);

  const [registerInnerModal] = useModalInner(async (record: any) => {
    showSkeleton.value = true;
    if (!record || !record.id) {
      showSkeleton.value = false;
      return;
    }

    try {
      // 如果传入的是完整记录，直接使用；否则通过API获取详情
      let data = record;
      if (!record.deviceNo) {
        data = await glassUsageLogInfo(record.id);
      }

      // 赋值给Description组件
      setDescProps({ data });
    } catch (error) {
      console.error('获取详情失败:', error);
    } finally {
      showSkeleton.value = false;
    }
  });

  const [registerDescription, { setDescProps }] = useDescription({
    column: 2,
    labelStyle: {
      width: '120px',
      minWidth: '120px',
    },
    schema: descSchema,
  });
</script>
