<template>
  <div class="task-exception-stats bg-white p-4 mt-4 rounded-lg">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
      <div
        v-for="(item, index) of statsModel"
        :key="index"
        class="stat-card bg-[rgba(0,52,119,0.04)] rounded-lg p-4 flex items-center space-x-2 cursor-pointer"
      >
        <img :src="item.icon" alt="" class="object-contain size-12" />

        <div class="flex-1 w-0">
          <span class="text-base text-gray-900">{{ item.label }}</span>
        </div>

        <div class="flex-none text-3xl font-bold text-primary-500">
          {{ item.value || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { getTaskExceptionStatistics } from '@/api/security/siteTaskStatisticsAnalysis';
  import type { TaskExceptionStats } from '@/api/security/siteTaskStatisticsAnalysis/model';
  import stats1 from '@/assets/images/statistics/task/stats-1.png?url';
  import stats2 from '@/assets/images/statistics/task/stats-2.png?url';
  import stats3 from '@/assets/images/statistics/task/stats-3.png?url';
  import stats4 from '@/assets/images/statistics/task/stats-4.png?url';

  defineOptions({ name: 'TaskExceptionStats' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const loading = ref(false);
  const statsData = ref<TaskExceptionStats>({
    totalEventCount: 0,
    vehicleEventCount: 0,
    controlEventCount: 0,
    warehouseEventCount: 0,
  });

  const statsModel = computed(() => {
    return [
      {
        label: '异常总数',
        value: statsData.value.totalEventCount,
        icon: stats1,
      },
      {
        label: '智慧检修异常数',
        value: statsData.value.vehicleEventCount,
        icon: stats2,
      },
      {
        label: '智慧作业异常数',
        value: statsData.value.controlEventCount,
        icon: stats3,
      },
      {
        label: '智慧料库异常数',
        value: statsData.value.warehouseEventCount,
        icon: stats4,
      },
    ];
  });

  // 获取统计数据
  async function fetchStatsData(params?: any) {
    try {
      loading.value = true;
      const response = await getTaskExceptionStatistics(params);
      statsData.value = response || {
        totalEventCount: 0,
        vehicleEventCount: 0,
        controlEventCount: 0,
        warehouseEventCount: 0,
      };
    } catch (error) {
      console.error('获取任务异常统计数据失败:', error);
      statsData.value = {
        totalEventCount: 0,
        vehicleEventCount: 0,
        controlEventCount: 0,
        warehouseEventCount: 0,
      };
    } finally {
      loading.value = false;
    }
  }

  // 监听查询参数变化
  // watch(
  //   () => props.searchParams,
  //   (newParams) => {
  //     fetchStatsData(newParams);
  //   },
  //   { deep: true, immediate: true },
  // );

  // 暴露刷新方法供父组件调用
  defineExpose({
    refresh: (params?: any) => fetchStatsData(params),
  });
</script>

<style scoped>
  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
</style>
