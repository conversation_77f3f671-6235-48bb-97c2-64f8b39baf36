<template>
  <div class="task-exception-trend bg-white p-4 rounded-lg shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-800 app-marker">任务异常事件趋势</h3>
    </div>

    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, type Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { getEventTrend } from '@/api/security/siteTaskStatisticsAnalysis';
  import type { TaskExceptionTrendItem } from '@/api/security/siteTaskStatisticsAnalysis/model';

  defineOptions({ name: 'TaskExceptionTrend' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const chartRef = ref<HTMLDivElement>();
  const loading = ref(false);
  const chartData = ref<TaskExceptionTrendItem[]>([]);

  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 获取图表数据
  async function fetchChartData(params?: any) {
    try {
      loading.value = true;
      const response = await getEventTrend(params);
      chartData.value = response || [];
      updateChart();
    } catch (error) {
      console.error('获取任务异常事件趋势数据失败:', error);
      chartData.value = [];
      updateChart();
    } finally {
      loading.value = false;
    }
  }

  // 更新图表
  function updateChart() {
    const dates = chartData.value.map((item) => item.dateStr || '');
    const totalEvents = chartData.value.map((item) => item.totalEventCount || 0);
    const vehicleEvents = chartData.value.map((item) => item.vehicleEventCount || 0);
    const controlEvents = chartData.value.map((item) => item.controlEventCount || 0);
    const warehouseEvents = chartData.value.map((item) => item.warehouseEventCount || 0);

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        bottom: 0,
        data: ['异常总数', '智慧检修异常', '智慧作业异常', '智慧料库异常'],
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        name: '异常数量',
        nameTextStyle: {
          color: '#666',
        },
      },
      series: [
        {
          name: '异常总数',
          type: 'line',
          data: totalEvents,
          smooth: true,
          // lineStyle: {
          //   color: '#ff4d4f',
          //   width: 3,
          // },
          // itemStyle: {
          //   color: '#ff4d4f',
          // },
          // areaStyle: {
          //   color: {
          //     type: 'linear',
          //     x: 0,
          //     y: 0,
          //     x2: 0,
          //     y2: 1,
          //     colorStops: [
          //       { offset: 0, color: 'rgba(255, 77, 79, 0.3)' },
          //       { offset: 1, color: 'rgba(255, 77, 79, 0.05)' },
          //     ],
          //   },
          // },
        },
        {
          name: '智慧检修异常',
          type: 'line',
          data: vehicleEvents,
          smooth: true,
          // lineStyle: {
          //   color: '#fa8c16',
          //   width: 2,
          // },
          // itemStyle: {
          //   color: '#fa8c16',
          // },
        },
        {
          name: '智慧作业异常',
          type: 'line',
          data: controlEvents,
          smooth: true,
          // lineStyle: {
          //   color: '#fadb14',
          //   width: 2,
          // },
          // itemStyle: {
          //   color: '#fadb14',
          // },
        },
        {
          name: '智慧料库异常',
          type: 'line',
          data: warehouseEvents,
          smooth: true,
          // lineStyle: {
          //   color: '#722ed1',
          //   width: 2,
          // },
          // itemStyle: {
          //   color: '#722ed1',
          // },
        },
      ],
    };

    setOptions(option as any);
  }

  // 监听查询参数变化
  // watch(
  //   () => props.searchParams,
  //   (newParams) => {
  //     fetchChartData(newParams);
  //   },
  //   { deep: true, immediate: true },
  // );

  onMounted(() => {
    window.addEventListener('resize', resize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
  });

  // 暴露刷新方法
  defineExpose({
    refresh: (params?: any) => fetchChartData(params),
  });
</script>

<style scoped>
  .task-exception-trend {
    transition: all 0.3s ease;
  }
</style>
