<template>
  <div class="filter-form bg-white p-4 rounded-lg shadow-sm">
    <a-form layout="inline" :model="formData" @finish="handleSearch">
      <a-form-item label="选择站点">
        <a-select
          v-model:value="formData.siteName"
          placeholder="请选择站点"
          style="width: 200px"
          allowClear
          :options="stationOptions"
          :field-names="{ label: 'stationName', value: 'stationName' }"
        />
      </a-form-item>

      <a-form-item label="时间范围">
        <!-- show-time -->
        <a-range-picker
          v-model:value="dateRange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          style="width: 350px"
          :presets="timePresets"
          @change="handleDateChange"
        />
      </a-form-item>

      <a-form-item>
        <a-button type="primary" html-type="submit" :loading="loading"> 查询 </a-button>
        <a-button style="margin-left: 8px" @click="handleReset"> 重置 </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue';
  import { stationOptionSelect } from '@/api/business/station';
  import dayjs, { Dayjs } from 'dayjs';
  import type { RangePickerProps } from 'ant-design-vue/es/date-picker';

  defineOptions({ name: 'TaskStatisticsFilterForm' });

  interface Props {
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
  });

  const emit = defineEmits<{
    search: [params: any];
  }>();

  // 表单数据
  const formData = reactive({
    siteName: undefined as string | undefined,
    startTime: '',
    endTime: '',
  });

  // 日期范围
  const dateRange = ref<[Dayjs, Dayjs] | undefined>();

  // 站点选项
  const stationOptions = ref<any[]>([]);

  // 时间快捷选项
  const timePresets = computed<RangePickerProps['presets']>(() => [
    { label: '近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
    { label: '近1个月', value: [dayjs().subtract(1, 'month'), dayjs()] },
    { label: '近3个月', value: [dayjs().subtract(3, 'month'), dayjs()] },
    { label: '近6个月', value: [dayjs().subtract(6, 'month'), dayjs()] },
    { label: '近1年', value: [dayjs().subtract(1, 'year'), dayjs()] },
  ]);

  // 获取站点选项
  async function fetchStationOptions() {
    try {
      const response = await stationOptionSelect();
      stationOptions.value = response || [];
    } catch (error) {
      console.error('获取站点选项失败:', error);
      stationOptions.value = [];
    }
  }

  // 处理日期变化
  function handleDateChange(dates: [Dayjs, Dayjs] | null) {
    if (dates) {
      formData.startTime = dayjs(dates[0]).format('YYYY-MM-DD');
      formData.endTime = dayjs(dates[1]).format('YYYY-MM-DD');
    } else {
      formData.startTime = '';
      formData.endTime = '';
    }
  }

  // 搜索
  function handleSearch() {
    emit('search', { ...formData });
  }

  // 重置
  function handleReset() {
    formData.siteName = stationOptions.value?.[0]?.stationName;
    formData.startTime = '';
    formData.endTime = '';
    dateRange.value = undefined;

    // 设置默认近7天
    const defaultRange: [Dayjs, Dayjs] = [dayjs().subtract(7, 'day'), dayjs()];
    dateRange.value = defaultRange;
    handleDateChange(defaultRange);

    handleSearch();
  }

  // 初始化
  onMounted(async () => {
    await fetchStationOptions();

    // 默认设置近7天
    const defaultRange: [Dayjs, Dayjs] = [dayjs().subtract(7, 'day'), dayjs()];
    dateRange.value = defaultRange;
    handleDateChange(defaultRange);

    // 自动触发一次查询
    handleSearch();
  });

  // 暴露刷新方法
  defineExpose({
    refresh: handleSearch,
  });
</script>

<style scoped>
  .filter-form {
    transition: all 0.3s ease;
  }
</style>
