import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';
import { t } from '@/hooks/web/useI18n';

const dashboard: AppRouteModule = {
  path: '/dashboard',
  name: 'Dashboard',
  component: LAYOUT,
  redirect: '/dashboard/index',
  meta: {
    orderNo: 10,
    icon: 'ion:grid-outline',
    title: t('routes.dashboard.dashboard'),
    hideChildrenInMenu: true,
    hideBreadcrumb: true,
  },
  children: [
    {
      path: 'index',
      name: 'Index',
      component: () => import('@/views/security/dashboard/index.vue'),
      meta: {
        affix: true,
        title: t('routes.dashboard.dashboard'),
      },
    },
  ],
};

export default dashboard;
